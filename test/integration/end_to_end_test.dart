import 'dart:convert';
import 'dart:io';
import 'package:openapi_dart_gen/openapi_dart_gen.dart';
import 'package:test/test.dart';
import 'package:path/path.dart' as path;

void main() {
  group('End-to-End Integration Tests', () {
    late Directory tempDir;

    setUp(() async {
      tempDir = await Directory.systemTemp.createTemp('e2e_test');
    });

    tearDown(() async {
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }
    });

    test('should generate complete working code from petstore-like API', () async {
      // Create a comprehensive test API specification
      final petstoreSpec = {
        'openapi': '3.0.0',
        'info': {
          'title': 'Pet Store API',
          'version': '1.0.0',
          'description': 'A sample Pet Store Server based on the OpenAPI 3.0 specification'
        },
        'servers': [
          {
            'url': 'https://petstore.swagger.io/v3'
          }
        ],
        'tags': [
          {
            'name': 'pet',
            'description': 'Everything about your Pets'
          },
          {
            'name': 'store',
            'description': 'Operations about user'
          }
        ],
        'paths': {
          '/pet': {
            'post': {
              'tags': ['pet'],
              'summary': 'Add a new pet to the store',
              'operationId': 'addPet',
              'requestBody': {
                'description': 'Create a new pet in the store',
                'content': {
                  'application/json': {
                    'schema': {
                      '\$ref': '#/components/schemas/Pet'
                    }
                  }
                },
                'required': true
              },
              'responses': {
                '200': {
                  'description': 'Successful operation',
                  'content': {
                    'application/json': {
                      'schema': {
                        '\$ref': '#/components/schemas/Pet'
                      }
                    }
                  }
                },
                '405': {
                  'description': 'Invalid input'
                }
              }
            }
          },
          '/pet/{petId}': {
            'get': {
              'tags': ['pet'],
              'summary': 'Find pet by ID',
              'operationId': 'getPetById',
              'parameters': [
                {
                  'name': 'petId',
                  'in': 'path',
                  'description': 'ID of pet to return',
                  'required': true,
                  'schema': {
                    'type': 'integer',
                    'format': 'int64'
                  }
                }
              ],
              'responses': {
                '200': {
                  'description': 'successful operation',
                  'content': {
                    'application/json': {
                      'schema': {
                        '\$ref': '#/components/schemas/Pet'
                      }
                    }
                  }
                },
                '400': {
                  'description': 'Invalid ID supplied'
                },
                '404': {
                  'description': 'Pet not found'
                }
              }
            },
            'delete': {
              'tags': ['pet'],
              'summary': 'Deletes a pet',
              'operationId': 'deletePet',
              'parameters': [
                {
                  'name': 'petId',
                  'in': 'path',
                  'description': 'Pet id to delete',
                  'required': true,
                  'schema': {
                    'type': 'integer',
                    'format': 'int64'
                  }
                }
              ],
              'responses': {
                '400': {
                  'description': 'Invalid pet value'
                }
              }
            }
          },
          '/pet/findByStatus': {
            'get': {
              'tags': ['pet'],
              'summary': 'Finds Pets by status',
              'operationId': 'findPetsByStatus',
              'parameters': [
                {
                  'name': 'status',
                  'in': 'query',
                  'description': 'Status values that need to be considered for filter',
                  'required': false,
                  'schema': {
                    'type': 'string',
                    'enum': ['available', 'pending', 'sold']
                  }
                }
              ],
              'responses': {
                '200': {
                  'description': 'successful operation',
                  'content': {
                    'application/json': {
                      'schema': {
                        'type': 'array',
                        'items': {
                          '\$ref': '#/components/schemas/Pet'
                        }
                      }
                    }
                  }
                },
                '400': {
                  'description': 'Invalid status value'
                }
              }
            }
          }
        },
        'components': {
          'schemas': {
            'Pet': {
              'type': 'object',
              'required': ['name', 'photoUrls'],
              'properties': {
                'id': {
                  'type': 'integer',
                  'format': 'int64',
                  'example': 10
                },
                'name': {
                  'type': 'string',
                  'example': 'doggie'
                },
                'category': {
                  '\$ref': '#/components/schemas/Category'
                },
                'photoUrls': {
                  'type': 'array',
                  'items': {
                    'type': 'string'
                  }
                },
                'tags': {
                  'type': 'array',
                  'items': {
                    '\$ref': '#/components/schemas/Tag'
                  }
                },
                'status': {
                  'type': 'string',
                  'description': 'pet status in the store',
                  'enum': ['available', 'pending', 'sold']
                }
              }
            },
            'Category': {
              'type': 'object',
              'properties': {
                'id': {
                  'type': 'integer',
                  'format': 'int64',
                  'example': 1
                },
                'name': {
                  'type': 'string',
                  'example': 'Dogs'
                }
              }
            },
            'Tag': {
              'type': 'object',
              'properties': {
                'id': {
                  'type': 'integer',
                  'format': 'int64'
                },
                'name': {
                  'type': 'string'
                }
              }
            }
          }
        }
      };

      // Write the specification to a file
      final specFile = File(path.join(tempDir.path, 'petstore.json'));
      await specFile.writeAsString(jsonEncode(petstoreSpec));

      // Generate code using the CodeGenerator
      final outputDir = Directory(path.join(tempDir.path, 'generated'));
      final generator = CodeGenerator();
      
      await generator.generateFromFile(specFile.path, outputDir.path);

      // Verify the generated structure
      expect(await outputDir.exists(), isTrue);

      // Check models directory and files
      final modelsDir = Directory(path.join(outputDir.path, 'models'));
      expect(await modelsDir.exists(), isTrue);

      final petModelFile = File(path.join(modelsDir.path, 'pet.dart'));
      final categoryModelFile = File(path.join(modelsDir.path, 'category.dart'));
      final tagModelFile = File(path.join(modelsDir.path, 'tag.dart'));
      final modelsBarrelFile = File(path.join(modelsDir.path, 'models.dart'));

      expect(await petModelFile.exists(), isTrue);
      expect(await categoryModelFile.exists(), isTrue);
      expect(await tagModelFile.exists(), isTrue);
      expect(await modelsBarrelFile.exists(), isTrue);

      // Check client directory and files
      final clientDir = Directory(path.join(outputDir.path, 'client'));
      expect(await clientDir.exists(), isTrue);

      final petServiceFile = File(path.join(clientDir.path, 'pet_service.dart'));
      final apiClientFile = File(path.join(clientDir.path, 'api_client.dart'));

      expect(await petServiceFile.exists(), isTrue);
      expect(await apiClientFile.exists(), isTrue);

      // Verify generated model content
      final petModelContent = await petModelFile.readAsString();
      expect(petModelContent, contains('class Pet {'));
      expect(petModelContent, contains('final int? id;'));
      expect(petModelContent, contains('final String name;'));
      expect(petModelContent, contains('final List<String> photoUrls;'));
      expect(petModelContent, contains('final Category? category;'));
      expect(petModelContent, contains('final List<Tag>? tags;'));
      expect(petModelContent, contains('factory Pet.fromJson'));
      expect(petModelContent, contains('Map<String, dynamic> toJson'));

      // Verify generated service content
      final petServiceContent = await petServiceFile.readAsString();
      expect(petServiceContent, contains('class PetService {'));
      expect(petServiceContent, contains('Future<Response<Pet>> addPet(Pet requestData)'));
      expect(petServiceContent, contains('Future<Response<Pet>> getPetById(int petId)'));
      expect(petServiceContent, contains('Future<Response<dynamic>> deletePet(int petId)'));
      expect(petServiceContent, contains('Future<Response<List<Pet>>> findPetsByStatus({String? status})'));

      // Verify API client content
      final apiClientContent = await apiClientFile.readAsString();
      expect(apiClientContent, contains('class ApiClient {'));
      expect(apiClientContent, contains('late final PetService pet;'));
      expect(apiClientContent, contains('pet = PetService(_dio);'));

      // Verify imports and error handling
      expect(petServiceContent, contains("import 'package:dio/dio.dart';"));
      expect(petServiceContent, contains("import '../models/models.dart';"));
      expect(petServiceContent, contains("import 'package:openapi_dart_gen/openapi_dart_gen.dart';"));
      expect(petServiceContent, contains('try {'));
      expect(petServiceContent, contains('} on DioException catch (e) {'));
      expect(petServiceContent, contains('ApiExceptionHandler.fromDioException(e)'));

      // Check that the generated code follows Dart conventions
      expect(petModelContent, contains('const Pet({'));
      expect(petModelContent, contains('required this.name,'));
      expect(petModelContent, contains('required this.photoUrls,'));
      expect(petServiceContent, contains('const PetService(this._dio);'));
    });

    test('should handle complex nested schemas and references', () async {
      final complexSpec = {
        'openapi': '3.0.0',
        'info': {
          'title': 'Complex API',
          'version': '1.0.0'
        },
        'paths': {
          '/complex': {
            'post': {
              'operationId': 'createComplex',
              'tags': ['complex'],
              'requestBody': {
                'content': {
                  'application/json': {
                    'schema': {
                      '\$ref': '#/components/schemas/ComplexObject'
                    }
                  }
                }
              },
              'responses': {
                '200': {
                  'description': 'Success',
                  'content': {
                    'application/json': {
                      'schema': {
                        '\$ref': '#/components/schemas/ComplexObject'
                      }
                    }
                  }
                }
              }
            }
          }
        },
        'components': {
          'schemas': {
            'ComplexObject': {
              'type': 'object',
              'properties': {
                'nested': {
                  '\$ref': '#/components/schemas/NestedObject'
                },
                'arrayOfObjects': {
                  'type': 'array',
                  'items': {
                    '\$ref': '#/components/schemas/SimpleObject'
                  }
                },
                'arrayOfPrimitives': {
                  'type': 'array',
                  'items': {
                    'type': 'string'
                  }
                }
              }
            },
            'NestedObject': {
              'type': 'object',
              'properties': {
                'value': {
                  'type': 'string'
                },
                'deepNested': {
                  '\$ref': '#/components/schemas/SimpleObject'
                }
              }
            },
            'SimpleObject': {
              'type': 'object',
              'properties': {
                'id': {
                  'type': 'integer'
                },
                'name': {
                  'type': 'string'
                }
              }
            }
          }
        }
      };

      final specFile = File(path.join(tempDir.path, 'complex.json'));
      await specFile.writeAsString(jsonEncode(complexSpec));

      final outputDir = Directory(path.join(tempDir.path, 'complex_generated'));
      final generator = CodeGenerator();
      
      await generator.generateFromFile(specFile.path, outputDir.path);

      // Verify all models were generated
      final complexObjectFile = File(path.join(outputDir.path, 'models', 'complex_object.dart'));
      final nestedObjectFile = File(path.join(outputDir.path, 'models', 'nested_object.dart'));
      final simpleObjectFile = File(path.join(outputDir.path, 'models', 'simple_object.dart'));

      expect(await complexObjectFile.exists(), isTrue);
      expect(await nestedObjectFile.exists(), isTrue);
      expect(await simpleObjectFile.exists(), isTrue);

      // Verify complex relationships are handled correctly
      final complexContent = await complexObjectFile.readAsString();
      expect(complexContent, contains('final NestedObject? nested;'));
      expect(complexContent, contains('final List<SimpleObject>? arrayOfObjects;'));
      expect(complexContent, contains('final List<String>? arrayOfPrimitives;'));
    });

    test('should generate code that compiles without errors', () async {
      // Use the existing example.json from the project
      final exampleFile = File('example.json');
      if (!await exampleFile.exists()) {
        // Skip this test if example.json doesn't exist
        return;
      }

      final outputDir = Directory(path.join(tempDir.path, 'compile_test'));
      final generator = CodeGenerator();
      
      await generator.generateFromFile(exampleFile.path, outputDir.path);

      // Run dart analyze on the generated code
      final analyzeResult = await Process.run(
        'dart',
        ['analyze', outputDir.path],
        workingDirectory: outputDir.path,
      );

      // The generated code should have no analysis errors
      // (warnings are acceptable, but no errors)
      expect(analyzeResult.exitCode, equals(0));
    });
  });
}
