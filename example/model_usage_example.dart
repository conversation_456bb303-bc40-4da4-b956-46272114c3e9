import '../test_example/models/models.dart';

/// Example demonstrating how to work with generated model classes
/// 
/// This example shows:
/// - Creating model instances
/// - JSON serialization and deserialization
/// - Working with nested objects and arrays
/// - Handling nullable fields
/// - Model validation and best practices
void main() {
  print('📦 OpenAPI Dart Generator - Model Usage Example');
  print('=================================================');

  // Example 1: Basic model creation and serialization
  _demonstrateBasicModelUsage();

  // Example 2: Working with nested objects
  _demonstrateNestedObjects();

  // Example 3: Handling arrays and lists
  _demonstrateArrayHandling();

  // Example 4: JSON serialization/deserialization
  _demonstrateJsonSerialization();

  // Example 5: Nullable fields and validation
  _demonstrateNullableFields();

  // Example 6: Model comparison and equality
  _demonstrateModelComparison();

  print('\n✅ Model usage examples completed!');
}

/// Demonstrate basic model creation and usage
void _demonstrateBasicModelUsage() {
  print('\n🏗️  Basic Model Usage');
  print('--------------------');

  // Create a user model with required fields
  final user = CustomUserOut(
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '+1234567890',
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  print('✅ Created user: ${user.name}');
  print('   ID: ${user.id}');
  print('   Email: ${user.email}');
  print('   Phone: ${user.phone}');
  print('   Created: ${user.createdAt}');

  // Create a login request model
  final loginRequest = LoginRequest(
    email: '<EMAIL>',
    password: 'securePassword123',
  );

  print('\n✅ Created login request for: ${loginRequest.email}');
}

/// Demonstrate working with nested objects
void _demonstrateNestedObjects() {
  print('\n🔗 Nested Objects Example');
  print('-------------------------');

  // Create a store with nested address information
  final store = StoreOut(
    id: 1,
    name: 'Coffee Paradise',
    description: 'The best coffee in town',
    address: '123 Main Street, Coffee City',
    phone: '+1555123456',
    email: '<EMAIL>',
    ownerId: 1,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  print('✅ Created store: ${store.name}');
  print('   Address: ${store.address}');
  print('   Owner ID: ${store.ownerId}');
  print('   Contact: ${store.email}');

  // Create an order with nested relationships
  final order = OrderOut(
    id: 1,
    userId: 1,
    storeId: store.id!,
    status: 'pending',
    totalAmount: 25.99,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  print('\n✅ Created order: #${order.id}');
  print('   Store ID: ${order.storeId}');
  print('   Status: ${order.status}');
  print('   Total: \$${order.totalAmount}');
}

/// Demonstrate handling arrays and lists
void _demonstrateArrayHandling() {
  print('\n📋 Array Handling Example');
  print('-------------------------');

  // Create multiple users
  final users = [
    CustomUserOut(
      id: 1,
      name: 'Alice Johnson',
      email: '<EMAIL>',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    CustomUserOut(
      id: 2,
      name: 'Bob Smith',
      email: '<EMAIL>',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    CustomUserOut(
      id: 3,
      name: 'Carol Davis',
      email: '<EMAIL>',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
  ];

  print('✅ Created ${users.length} users:');
  for (final user in users) {
    print('   👤 ${user.name} (${user.email})');
  }

  // Create a paginated response
  final paginatedUsers = PaginatedCustomUserOutList(
    count: users.length,
    results: users,
  );

  print('\n✅ Created paginated response:');
  print('   Total count: ${paginatedUsers.count}');
  print('   Results: ${paginatedUsers.results?.length ?? 0} users');
}

/// Demonstrate JSON serialization and deserialization
void _demonstrateJsonSerialization() {
  print('\n🔄 JSON Serialization Example');
  print('-----------------------------');

  // Create a user model
  final originalUser = CustomUserOut(
    id: 42,
    name: 'Jane Developer',
    email: '<EMAIL>',
    phone: '+1555987654',
    createdAt: DateTime.parse('2024-01-15T10:30:00Z'),
    updatedAt: DateTime.parse('2024-01-15T10:30:00Z'),
  );

  print('✅ Original user: ${originalUser.name}');

  // Serialize to JSON
  final json = originalUser.toJson();
  print('✅ Serialized to JSON:');
  print('   ${json.toString()}');

  // Deserialize from JSON
  final deserializedUser = CustomUserOut.fromJson(json);
  print('✅ Deserialized user: ${deserializedUser.name}');
  print('   ID matches: ${originalUser.id == deserializedUser.id}');
  print('   Name matches: ${originalUser.name == deserializedUser.name}');
  print('   Email matches: ${originalUser.email == deserializedUser.email}');

  // Test with complex nested JSON
  final complexJson = {
    'count': 2,
    'next': null,
    'previous': null,
    'results': [
      {
        'id': 1,
        'name': 'User One',
        'email': '<EMAIL>',
        'phone': '+1111111111',
        'created_at': '2024-01-01T00:00:00Z',
        'updated_at': '2024-01-01T00:00:00Z',
      },
      {
        'id': 2,
        'name': 'User Two',
        'email': '<EMAIL>',
        'phone': '+2222222222',
        'created_at': '2024-01-02T00:00:00Z',
        'updated_at': '2024-01-02T00:00:00Z',
      },
    ],
  };

  final paginatedResponse = PaginatedCustomUserOutList.fromJson(complexJson);
  print('\n✅ Deserialized paginated response:');
  print('   Count: ${paginatedResponse.count}');
  print('   Users: ${paginatedResponse.results?.length ?? 0}');
  paginatedResponse.results?.forEach((user) {
    print('     - ${user.name} (${user.email})');
  });
}

/// Demonstrate handling nullable fields
void _demonstrateNullableFields() {
  print('\n❓ Nullable Fields Example');
  print('-------------------------');

  // Create user with minimal required fields
  final minimalUser = CustomUserOut(
    id: 100,
    name: 'Minimal User',
    email: '<EMAIL>',
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
    // phone is nullable and not provided
  );

  print('✅ Created minimal user: ${minimalUser.name}');
  print('   Phone provided: ${minimalUser.phone != null}');
  print('   Phone value: ${minimalUser.phone ?? 'Not provided'}');

  // Create user with all fields
  final completeUser = CustomUserOut(
    id: 101,
    name: 'Complete User',
    email: '<EMAIL>',
    phone: '+1555000000',
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  print('\n✅ Created complete user: ${completeUser.name}');
  print('   Phone provided: ${completeUser.phone != null}');
  print('   Phone value: ${completeUser.phone}');

  // Demonstrate safe access to nullable fields
  final phoneDisplay = completeUser.phone?.replaceAll('+1', '') ?? 'No phone';
  print('   Formatted phone: $phoneDisplay');
}

/// Demonstrate model comparison and equality
void _demonstrateModelComparison() {
  print('\n⚖️  Model Comparison Example');
  print('---------------------------');

  final user1 = CustomUserOut(
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
    updatedAt: DateTime.parse('2024-01-01T00:00:00Z'),
  );

  final user2 = CustomUserOut(
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
    updatedAt: DateTime.parse('2024-01-01T00:00:00Z'),
  );

  final user3 = CustomUserOut(
    id: 2,
    name: 'Jane Doe',
    email: '<EMAIL>',
    createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
    updatedAt: DateTime.parse('2024-01-01T00:00:00Z'),
  );

  print('✅ Created three users for comparison');
  
  // Note: Generated models don't automatically implement equality
  // You would need to implement this manually or use packages like equatable
  print('   User1 ID: ${user1.id}, Name: ${user1.name}');
  print('   User2 ID: ${user2.id}, Name: ${user2.name}');
  print('   User3 ID: ${user3.id}, Name: ${user3.name}');
  
  // Manual comparison by fields
  final sameId = user1.id == user2.id;
  final sameName = user1.name == user2.name;
  final sameEmail = user1.email == user2.email;
  
  print('   User1 vs User2 - Same ID: $sameId, Same Name: $sameName, Same Email: $sameEmail');
  print('   User1 vs User3 - Same ID: ${user1.id == user3.id}');
}

/// Helper function to demonstrate model validation
bool _validateUser(CustomUserOut user) {
  if (user.name.isEmpty) {
    print('❌ Validation failed: Name cannot be empty');
    return false;
  }
  
  if (!user.email.contains('@')) {
    print('❌ Validation failed: Invalid email format');
    return false;
  }
  
  if (user.phone != null && user.phone!.length < 10) {
    print('❌ Validation failed: Phone number too short');
    return false;
  }
  
  print('✅ User validation passed');
  return true;
}
