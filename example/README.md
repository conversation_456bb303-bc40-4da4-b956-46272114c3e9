# OpenAPI Dart Generator - Example Project

This directory contains examples demonstrating how to use the OpenAPI Dart Generator and the generated code.

## 📁 Files

- `api_client_example.dart` - Complete example showing API client usage
- `model_usage_example.dart` - Examples of working with generated models
- `error_handling_example.dart` - Comprehensive error handling patterns
- `testing_example.dart` - How to test code using generated clients

## 🚀 Running the Examples

### Prerequisites

1. Make sure you have Dart SDK installed
2. Run the code generator to create the test example:

```bash
# From the project root
dart run bin/openapi_dart_gen.dart generate -i example.json -o test_example
```

### Run the Main Example

```bash
# From the project root
dart run example/api_client_example.dart
```

## 📖 What You'll Learn

### 1. Basic API Client Setup

```dart
// Configure Dio
final dio = Dio(BaseOptions(
  baseUrl: 'https://api.example.com',
  connectTimeout: Duration(seconds: 10),
));

// Create API client
final apiClient = ApiClient(dio);
```

### 2. Making API Calls

```dart
// GET request with query parameters
final users = await apiClient.users.getUsers(
  page: 1,
  limit: 10,
  search: 'john',
);

// POST request with request body
final newUser = User(name: '<PERSON> Doe', email: '<EMAIL>');
final created = await apiClient.users.createUser(newUser);
```

### 3. Error Handling

```dart
try {
  final user = await apiClient.users.getUser(123);
} on NotFoundException catch (e) {
  print('User not found: ${e.message}');
} on ValidationException catch (e) {
  print('Validation failed: ${e.message}');
} on ApiException catch (e) {
  print('API error: ${e.message} (${e.statusCode})');
}
```

### 4. Working with Models

```dart
// Create model instance
final user = User(
  id: 1,
  name: 'John Doe',
  email: '<EMAIL>',
);

// Serialize to JSON
final json = user.toJson();

// Deserialize from JSON
final userFromJson = User.fromJson(json);
```

## 🧪 Testing Examples

The examples include patterns for:

- Unit testing generated models
- Mocking API clients for testing
- Integration testing with real APIs
- Error scenario testing

## 🔧 Advanced Configuration

Learn how to:

- Configure Dio interceptors
- Handle authentication
- Implement retry logic
- Add request/response logging
- Handle different environments

## 📚 Additional Resources

- [Main README](../README.md) - Complete package documentation
- [API Documentation](https://pub.dev/documentation/openapi_dart_gen)
- [Generated Code Structure](../README.md#generated-code-structure)

## 🤝 Contributing

Found an issue with the examples or have suggestions for improvements? Please:

1. Check existing [issues](https://github.com/your-username/openapi_dart_gen/issues)
2. Create a new issue with details
3. Submit a pull request with improvements

## 📄 License

These examples are part of the OpenAPI Dart Generator project and are licensed under the MIT License.
