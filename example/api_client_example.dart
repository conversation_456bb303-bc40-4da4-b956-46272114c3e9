import 'package:dio/dio.dart';
import '../test_example/api.dart';

/// Example demonstrating how to use the generated API client
/// 
/// This example shows:
/// - Setting up the Dio client with configuration
/// - Creating the API client instance
/// - Making API calls with proper error handling
/// - Using generated models for type safety
void main() async {
  print('🚀 OpenAPI Dart Generator - API Client Example');
  print('================================================');

  // 1. Configure Dio with base settings
  final dio = Dio(BaseOptions(
    baseUrl: 'https://jsonplaceholder.typicode.com', // Mock API for demo
    connectTimeout: Duration(seconds: 10),
    receiveTimeout: Duration(seconds: 5),
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'OpenAPI-Dart-Gen-Example/1.0.0',
    },
  ));

  // 2. Add logging interceptor for debugging
  dio.interceptors.add(LogInterceptor(
    requestBody: true,
    responseBody: true,
    requestHeader: false,
    responseHeader: false,
    logPrint: (obj) => print('🌐 HTTP: $obj'),
  ));

  // 3. Add error handling interceptor
  dio.interceptors.add(InterceptorsWrapper(
    onError: (error, handler) {
      print('❌ HTTP Error: ${error.response?.statusCode} - ${error.message}');
      handler.next(error);
    },
  ));

  // 4. Create the API client using generated code
  final apiClient = ApiClient(dio);

  print('\n📋 Testing Generated API Client');
  print('--------------------------------');

  try {
    // Example 1: Authentication (if available in your API)
    await _demonstrateAuthentication(apiClient);

    // Example 2: User operations
    await _demonstrateUserOperations(apiClient);

    // Example 3: Error handling
    await _demonstrateErrorHandling(apiClient);

    // Example 4: Complex operations with query parameters
    await _demonstrateComplexOperations(apiClient);

  } catch (e) {
    print('💥 Unexpected error: $e');
  } finally {
    // Clean up
    dio.close();
    print('\n✅ Example completed successfully!');
  }
}

/// Demonstrate authentication operations
Future<void> _demonstrateAuthentication(ApiClient apiClient) async {
  print('\n🔐 Authentication Example');
  print('-------------------------');

  try {
    // Example login request (adjust based on your API)
    final loginRequest = LoginRequest(
      email: '<EMAIL>',
      password: 'password123',
    );

    print('📤 Attempting login...');
    final loginResponse = await apiClient.auth.coreApiSignin(loginRequest);
    
    print('✅ Login successful!');
    print('   Token: ${loginResponse.data?.token?.substring(0, 20)}...');
    print('   User: ${loginResponse.data?.user?.name}');

  } on UnauthorizedException catch (e) {
    print('🚫 Authentication failed: ${e.message}');
  } on ValidationException catch (e) {
    print('⚠️  Validation error: ${e.message}');
  } on ApiException catch (e) {
    print('❌ API error: ${e.message} (${e.statusCode})');
  } catch (e) {
    print('💥 Unexpected error during authentication: $e');
  }
}

/// Demonstrate user operations
Future<void> _demonstrateUserOperations(ApiClient apiClient) async {
  print('\n👥 User Operations Example');
  print('--------------------------');

  try {
    // Get all users with pagination
    print('📤 Fetching users...');
    final usersResponse = await apiClient.users.usersApiUserListUsers(
      page: 1,
      pageSize: 5,
    );

    print('✅ Retrieved ${usersResponse.data?.results?.length ?? 0} users');
    
    // Display user information
    usersResponse.data?.results?.take(3).forEach((user) {
      print('   👤 ${user.name} (${user.email})');
    });

    // Create a new user (if supported)
    if (usersResponse.data?.results?.isNotEmpty == true) {
      print('\n📤 Creating new user...');
      
      final newUser = CustomUserIn(
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
      );

      final createdUserResponse = await apiClient.users.usersApiUserCreateUser(newUser);
      print('✅ User created with ID: ${createdUserResponse.data?.id}');
    }

  } on NotFoundException catch (e) {
    print('🔍 Resource not found: ${e.message}');
  } on ValidationException catch (e) {
    print('⚠️  Validation error: ${e.message}');
  } on ApiException catch (e) {
    print('❌ API error: ${e.message} (${e.statusCode})');
  } catch (e) {
    print('💥 Unexpected error during user operations: $e');
  }
}

/// Demonstrate error handling scenarios
Future<void> _demonstrateErrorHandling(ApiClient apiClient) async {
  print('\n🚨 Error Handling Example');
  print('-------------------------');

  // Test different error scenarios
  final errorScenarios = [
    () async {
      print('📤 Testing 404 error...');
      await apiClient.users.usersApiUserRetrieveUser(99999);
    },
    () async {
      print('📤 Testing validation error...');
      final invalidUser = CustomUserIn(
        name: '', // Invalid empty name
        email: 'invalid-email', // Invalid email format
      );
      await apiClient.users.usersApiUserCreateUser(invalidUser);
    },
  ];

  for (final scenario in errorScenarios) {
    try {
      await scenario();
    } on NotFoundException catch (e) {
      print('✅ Caught NotFoundException: ${e.message}');
    } on ValidationException catch (e) {
      print('✅ Caught ValidationException: ${e.message}');
    } on ClientException catch (e) {
      print('✅ Caught ClientException: ${e.message} (${e.statusCode})');
    } on ServerException catch (e) {
      print('✅ Caught ServerException: ${e.message} (${e.statusCode})');
    } on NetworkException catch (e) {
      print('✅ Caught NetworkException: ${e.message}');
    } on TimeoutException catch (e) {
      print('✅ Caught TimeoutException: ${e.message}');
    } on ApiException catch (e) {
      print('✅ Caught ApiException: ${e.message} (${e.statusCode})');
    } catch (e) {
      print('❌ Unexpected error: $e');
    }
  }
}

/// Demonstrate complex operations with query parameters
Future<void> _demonstrateComplexOperations(ApiClient apiClient) async {
  print('\n🔍 Complex Operations Example');
  print('-----------------------------');

  try {
    // Search with multiple parameters
    print('📤 Searching stores with filters...');
    final storesResponse = await apiClient.stores.storesApiStoreListStores(
      page: 1,
      pageSize: 10,
      search: 'coffee',
      ownerId: 1,
    );

    print('✅ Found ${storesResponse.data?.results?.length ?? 0} stores');
    
    storesResponse.data?.results?.forEach((store) {
      print('   🏪 ${store.name} - ${store.address}');
    });

    // Get orders with date filtering
    print('\n📤 Fetching recent orders...');
    final ordersResponse = await apiClient.orders.ordersApiOrderListOrders(
      page: 1,
      pageSize: 5,
    );

    print('✅ Retrieved ${ordersResponse.data?.results?.length ?? 0} orders');
    
    ordersResponse.data?.results?.forEach((order) {
      print('   📦 Order #${order.id} - Status: ${order.status}');
    });

  } on ApiException catch (e) {
    print('❌ API error: ${e.message} (${e.statusCode})');
  } catch (e) {
    print('💥 Unexpected error during complex operations: $e');
  }
}

/// Example of custom Dio configuration for advanced use cases
void _demonstrateAdvancedConfiguration() {
  print('\n⚙️  Advanced Configuration Example');
  print('----------------------------------');

  final dio = Dio(BaseOptions(
    baseUrl: 'https://api.example.com',
    connectTimeout: Duration(seconds: 15),
    receiveTimeout: Duration(seconds: 10),
    sendTimeout: Duration(seconds: 10),
  ));

  // Add retry interceptor
  dio.interceptors.add(InterceptorsWrapper(
    onError: (error, handler) async {
      if (error.response?.statusCode == 401) {
        // Handle token refresh
        print('🔄 Token expired, refreshing...');
        // Implement token refresh logic here
      }
      handler.next(error);
    },
  ));

  // Add request/response transformation
  dio.interceptors.add(InterceptorsWrapper(
    onRequest: (options, handler) {
      // Add authentication header
      final token = 'your-auth-token';
      options.headers['Authorization'] = 'Bearer $token';
      
      // Add request ID for tracing
      options.headers['X-Request-ID'] = DateTime.now().millisecondsSinceEpoch.toString();
      
      handler.next(options);
    },
    onResponse: (response, handler) {
      // Log response time
      final requestTime = response.requestOptions.extra['request_time'] as DateTime?;
      if (requestTime != null) {
        final duration = DateTime.now().difference(requestTime);
        print('⏱️  Request took ${duration.inMilliseconds}ms');
      }
      handler.next(response);
    },
  ));

  final apiClient = ApiClient(dio);
  print('✅ Advanced API client configured');
}
