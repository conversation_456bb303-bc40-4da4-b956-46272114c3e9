import 'package:openapi_dart_gen/openapi_dart_gen.dart';

void main() async {
  print('OpenAPI Dart Generator Example');
  
  // Create a parser instance
  final parser = OpenApiParser();
  print('Parser created: ${parser.runtimeType}');
  
  // Create a code generator instance
  final generator = CodeGenerator();
  print('Generator created: ${generator.runtimeType}');
  
  print('Example completed successfully!');
}
