// Generated model class for RegionIn
// This file is auto-generated. Do not edit manually.

/// Schema for creating a new region
class RegionIn {
  final String name;

  final String type;

  final int? parentId;

  final String? code;

  final String slug;

  final bool? isActive;

  const RegionIn({
    required this.name,
    required this.type,
    this.parentId,
    this.code,
    required this.slug,
    this.isActive,
  });

  factory RegionIn.fromJson(Map<String, dynamic> json) {
    return RegionIn(
      name: json['name']?.toString() ?? '',
      type: json['type']?.toString() ?? '',
      parentId: json['parent_id'] as int?,
      code: json['code'] as String?,
      slug: json['slug']?.toString() ?? '',
      isActive: json['is_active'] as bool?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'type': type,
      'parent_id': parentId,
      'code': code,
      'slug': slug,
      'is_active': isActive,
    };
  }

  @override
  String toString() {
    return 'RegionIn(name: $name, type: $type, parent_id: $parentId, code: $code, slug: $slug, is_active: $isActive)';
  }

  @override
  int get hashCode {
    return name.hashCode ^ type.hashCode ^ parentId.hashCode ^ code.hashCode ^ slug.hashCode ^ isActive.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! RegionIn) return false;
    return name == other.name && type == other.type && parentId == other.parentId && code == other.code && slug == other.slug && isActive == other.isActive;
  }

}
