// Generated model class for PaginatedStoreResponse
// This file is auto-generated. Do not edit manually.

import 'models.dart';

/// Paginated response for stores
class PaginatedStoreResponse {
  final List<StoreOut> stores;

  final int totalCount;

  final int page;

  final int pageSize;

  final int totalPages;

  final bool hasNext;

  final bool hasPrevious;

  const PaginatedStoreResponse({
    required this.stores,
    required this.totalCount,
    required this.page,
    required this.pageSize,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrevious,
  });

  factory PaginatedStoreResponse.fromJson(Map<String, dynamic> json) {
    return PaginatedStoreResponse(
      stores: (json['stores'] as List<dynamic>?)?.map((item) => StoreOut.fromJson(item as Map<String, dynamic>)).toList() ?? [],
      totalCount: json['total_count'] as int? ?? 0,
      page: json['page'] as int? ?? 0,
      pageSize: json['page_size'] as int? ?? 0,
      totalPages: json['total_pages'] as int? ?? 0,
      hasNext: json['has_next'] as bool? ?? false,
      hasPrevious: json['has_previous'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'stores': stores.map((item) => item.toJson()).toList(),
      'total_count': totalCount,
      'page': page,
      'page_size': pageSize,
      'total_pages': totalPages,
      'has_next': hasNext,
      'has_previous': hasPrevious,
    };
  }

  @override
  String toString() {
    return 'PaginatedStoreResponse(stores: $stores, total_count: $totalCount, page: $page, page_size: $pageSize, total_pages: $totalPages, has_next: $hasNext, has_previous: $hasPrevious)';
  }

  @override
  int get hashCode {
    return stores.hashCode ^ totalCount.hashCode ^ page.hashCode ^ pageSize.hashCode ^ totalPages.hashCode ^ hasNext.hashCode ^ hasPrevious.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! PaginatedStoreResponse) return false;
    return stores == other.stores && totalCount == other.totalCount && page == other.page && pageSize == other.pageSize && totalPages == other.totalPages && hasNext == other.hasNext && hasPrevious == other.hasPrevious;
  }

}
