// Generated model class for NotificationUpdate
// This file is auto-generated. Do not edit manually.

/// Schema for updating notification data
class NotificationUpdate {
  final String? title;

  final String? body;

  final bool? isRead;

  const NotificationUpdate({
    this.title,
    this.body,
    this.isRead,
  });

  factory NotificationUpdate.fromJson(Map<String, dynamic> json) {
    return NotificationUpdate(
      title: json['title'] as String?,
      body: json['body'] as String?,
      isRead: json['is_read'] as bool?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'body': body,
      'is_read': isRead,
    };
  }

  @override
  String toString() {
    return 'NotificationUpdate(title: $title, body: $body, is_read: $isRead)';
  }

  @override
  int get hashCode {
    return title.hashCode ^ body.hashCode ^ isRead.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! NotificationUpdate) return false;
    return title == other.title && body == other.body && isRead == other.isRead;
  }

}
