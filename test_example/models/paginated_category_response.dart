// Generated model class for PaginatedCategoryResponse
// This file is auto-generated. Do not edit manually.

import 'models.dart';

/// Paginated response for categories
class PaginatedCategoryResponse {
  final List<CategoryOut> categories;

  final int totalCount;

  final int page;

  final int pageSize;

  final int totalPages;

  final bool hasNext;

  final bool hasPrevious;

  const PaginatedCategoryResponse({
    required this.categories,
    required this.totalCount,
    required this.page,
    required this.pageSize,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrevious,
  });

  factory PaginatedCategoryResponse.fromJson(Map<String, dynamic> json) {
    return PaginatedCategoryResponse(
      categories: (json['categories'] as List<dynamic>?)?.map((item) => CategoryOut.fromJson(item as Map<String, dynamic>)).toList() ?? [],
      totalCount: json['total_count'] as int? ?? 0,
      page: json['page'] as int? ?? 0,
      pageSize: json['page_size'] as int? ?? 0,
      totalPages: json['total_pages'] as int? ?? 0,
      hasNext: json['has_next'] as bool? ?? false,
      hasPrevious: json['has_previous'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'categories': categories.map((item) => item.toJson()).toList(),
      'total_count': totalCount,
      'page': page,
      'page_size': pageSize,
      'total_pages': totalPages,
      'has_next': hasNext,
      'has_previous': hasPrevious,
    };
  }

  @override
  String toString() {
    return 'PaginatedCategoryResponse(categories: $categories, total_count: $totalCount, page: $page, page_size: $pageSize, total_pages: $totalPages, has_next: $hasNext, has_previous: $hasPrevious)';
  }

  @override
  int get hashCode {
    return categories.hashCode ^ totalCount.hashCode ^ page.hashCode ^ pageSize.hashCode ^ totalPages.hashCode ^ hasNext.hashCode ^ hasPrevious.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! PaginatedCategoryResponse) return false;
    return categories == other.categories && totalCount == other.totalCount && page == other.page && pageSize == other.pageSize && totalPages == other.totalPages && hasNext == other.hasNext && hasPrevious == other.hasPrevious;
  }

}
