// Generated model class for RegionOut
// This file is auto-generated. Do not edit manually.

/// Simplified region schema for store responses
class RegionOut {
  final int id;

  final String name;

  final String type;

  final String fullPath;

  const RegionOut({
    required this.id,
    required this.name,
    required this.type,
    required this.fullPath,
  });

  factory RegionOut.fromJson(Map<String, dynamic> json) {
    return RegionOut(
      id: json['id'] as int? ?? 0,
      name: json['name']?.toString() ?? '',
      type: json['type']?.toString() ?? '',
      fullPath: json['full_path']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'full_path': fullPath,
    };
  }

  @override
  String toString() {
    return 'RegionOut(id: $id, name: $name, type: $type, full_path: $fullPath)';
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ type.hashCode ^ fullPath.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! RegionOut) return false;
    return id == other.id && name == other.name && type == other.type && fullPath == other.fullPath;
  }

}
