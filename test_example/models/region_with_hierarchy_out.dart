// Generated model class for RegionWithHierarchyOut
// This file is auto-generated. Do not edit manually.

/// Schema for region output with hierarchy information
class RegionWithHierarchyOut {
  final int id;

  final String name;

  final String type;

  final int? parentId;

  final String? code;

  final String slug;

  final bool isActive;

  final String fullPath;

  final String hierarchicalName;

  final String createdAt;

  final String updatedAt;

  const RegionWithHierarchyOut({
    required this.id,
    required this.name,
    required this.type,
    this.parentId,
    this.code,
    required this.slug,
    required this.isActive,
    required this.fullPath,
    required this.hierarchicalName,
    required this.createdAt,
    required this.updatedAt,
  });

  factory RegionWithHierarchyOut.fromJson(Map<String, dynamic> json) {
    return RegionWithHierarchyOut(
      id: json['id'] as int? ?? 0,
      name: json['name']?.toString() ?? '',
      type: json['type']?.toString() ?? '',
      parentId: json['parent_id'] as int?,
      code: json['code'] as String?,
      slug: json['slug']?.toString() ?? '',
      isActive: json['is_active'] as bool? ?? false,
      fullPath: json['full_path']?.toString() ?? '',
      hierarchicalName: json['hierarchical_name']?.toString() ?? '',
      createdAt: json['created_at']?.toString() ?? '',
      updatedAt: json['updated_at']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'parent_id': parentId,
      'code': code,
      'slug': slug,
      'is_active': isActive,
      'full_path': fullPath,
      'hierarchical_name': hierarchicalName,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  @override
  String toString() {
    return 'RegionWithHierarchyOut(id: $id, name: $name, type: $type, parent_id: $parentId, code: $code, slug: $slug, is_active: $isActive, full_path: $fullPath, hierarchical_name: $hierarchicalName, created_at: $createdAt, updated_at: $updatedAt)';
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ type.hashCode ^ parentId.hashCode ^ code.hashCode ^ slug.hashCode ^ isActive.hashCode ^ fullPath.hashCode ^ hierarchicalName.hashCode ^ createdAt.hashCode ^ updatedAt.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! RegionWithHierarchyOut) return false;
    return id == other.id && name == other.name && type == other.type && parentId == other.parentId && code == other.code && slug == other.slug && isActive == other.isActive && fullPath == other.fullPath && hierarchicalName == other.hierarchicalName && createdAt == other.createdAt && updatedAt == other.updatedAt;
  }

}
