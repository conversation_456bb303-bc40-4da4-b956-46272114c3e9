// Generated model class for CustomUserOut
// This file is auto-generated. Do not edit manually.

/// Simplified user schema for wholesaler responses
class CustomUserOut {
  final int id;

  final String username;

  final String phone;

  const CustomUserOut({
    required this.id,
    required this.username,
    required this.phone,
  });

  factory CustomUserOut.fromJson(Map<String, dynamic> json) {
    return CustomUserOut(
      id: json['id'] as int? ?? 0,
      username: json['username']?.toString() ?? '',
      phone: json['phone']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'phone': phone,
    };
  }

  @override
  String toString() {
    return 'CustomUserOut(id: $id, username: $username, phone: $phone)';
  }

  @override
  int get hashCode {
    return id.hashCode ^ username.hashCode ^ phone.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CustomUserOut) return false;
    return id == other.id && username == other.username && phone == other.phone;
  }

}
