// Generated model class for OrderIn
// This file is auto-generated. Do not edit manually.

/// Schema for creating a new order
class OrderIn {
  final int wholesalerId;

  final int storeId;

  final double totalPrice;

  final double fees;

  final double productsTotalPrice;

  final int productsTotalQuantity;

  final String? deliverAt;

  const OrderIn({
    required this.wholesalerId,
    required this.storeId,
    required this.totalPrice,
    required this.fees,
    required this.productsTotalPrice,
    required this.productsTotalQuantity,
    this.deliverAt,
  });

  factory OrderIn.fromJson(Map<String, dynamic> json) {
    return OrderIn(
      wholesalerId: json['wholesaler_id'] as int? ?? 0,
      storeId: json['store_id'] as int? ?? 0,
      totalPrice: (json['total_price'] as num?)?.toDouble() ?? 0.0,
      fees: (json['fees'] as num?)?.toDouble() ?? 0.0,
      productsTotalPrice: (json['products_total_price'] as num?)?.toDouble() ?? 0.0,
      productsTotalQuantity: json['products_total_quantity'] as int? ?? 0,
      deliverAt: json['deliver_at'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'wholesaler_id': wholesalerId,
      'store_id': storeId,
      'total_price': totalPrice,
      'fees': fees,
      'products_total_price': productsTotalPrice,
      'products_total_quantity': productsTotalQuantity,
      'deliver_at': deliverAt,
    };
  }

  @override
  String toString() {
    return 'OrderIn(wholesaler_id: $wholesalerId, store_id: $storeId, total_price: $totalPrice, fees: $fees, products_total_price: $productsTotalPrice, products_total_quantity: $productsTotalQuantity, deliver_at: $deliverAt)';
  }

  @override
  int get hashCode {
    return wholesalerId.hashCode ^ storeId.hashCode ^ totalPrice.hashCode ^ fees.hashCode ^ productsTotalPrice.hashCode ^ productsTotalQuantity.hashCode ^ deliverAt.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! OrderIn) return false;
    return wholesalerId == other.wholesalerId && storeId == other.storeId && totalPrice == other.totalPrice && fees == other.fees && productsTotalPrice == other.productsTotalPrice && productsTotalQuantity == other.productsTotalQuantity && deliverAt == other.deliverAt;
  }

}
