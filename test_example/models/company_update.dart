// Generated model class for CompanyUpdate
// This file is auto-generated. Do not edit manually.

/// Schema for updating company data
class CompanyUpdate {
  final String? name;

  final String? title;

  final String? slug;

  const CompanyUpdate({
    this.name,
    this.title,
    this.slug,
  });

  factory CompanyUpdate.fromJson(Map<String, dynamic> json) {
    return CompanyUpdate(
      name: json['name'] as String?,
      title: json['title'] as String?,
      slug: json['slug'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'title': title,
      'slug': slug,
    };
  }

  @override
  String toString() {
    return 'CompanyUpdate(name: $name, title: $title, slug: $slug)';
  }

  @override
  int get hashCode {
    return name.hashCode ^ title.hashCode ^ slug.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CompanyUpdate) return false;
    return name == other.name && title == other.title && slug == other.slug;
  }

}
