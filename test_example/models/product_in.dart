// Generated model class for ProductIn
// This file is auto-generated. Do not edit manually.

/// Schema for creating a new product
class ProductIn {
  final String name;

  final String title;

  final String barcode;

  final String slug;

  final String? description;

  final int? companyId;

  final int? categoryId;

  final String? unit;

  final double? unitCount;

  const ProductIn({
    required this.name,
    required this.title,
    required this.barcode,
    required this.slug,
    this.description,
    this.companyId,
    this.categoryId,
    this.unit,
    this.unitCount,
  });

  factory ProductIn.fromJson(Map<String, dynamic> json) {
    return ProductIn(
      name: json['name']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      barcode: json['barcode']?.toString() ?? '',
      slug: json['slug']?.toString() ?? '',
      description: json['description'] as String?,
      companyId: json['company_id'] as int?,
      categoryId: json['category_id'] as int?,
      unit: json['unit'] as String?,
      unitCount: (json['unit_count'] as num?)?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'title': title,
      'barcode': barcode,
      'slug': slug,
      'description': description,
      'company_id': companyId,
      'category_id': categoryId,
      'unit': unit,
      'unit_count': unitCount,
    };
  }

  @override
  String toString() {
    return 'ProductIn(name: $name, title: $title, barcode: $barcode, slug: $slug, description: $description, company_id: $companyId, category_id: $categoryId, unit: $unit, unit_count: $unitCount)';
  }

  @override
  int get hashCode {
    return name.hashCode ^ title.hashCode ^ barcode.hashCode ^ slug.hashCode ^ description.hashCode ^ companyId.hashCode ^ categoryId.hashCode ^ unit.hashCode ^ unitCount.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ProductIn) return false;
    return name == other.name && title == other.title && barcode == other.barcode && slug == other.slug && description == other.description && companyId == other.companyId && categoryId == other.categoryId && unit == other.unit && unitCount == other.unitCount;
  }

}
