// Generated model class for CompanyIn
// This file is auto-generated. Do not edit manually.

/// Schema for creating a new company
class CompanyIn {
  final String name;

  final String title;

  final String slug;

  const CompanyIn({
    required this.name,
    required this.title,
    required this.slug,
  });

  factory CompanyIn.fromJson(Map<String, dynamic> json) {
    return CompanyIn(
      name: json['name']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      slug: json['slug']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'title': title,
      'slug': slug,
    };
  }

  @override
  String toString() {
    return 'CompanyIn(name: $name, title: $title, slug: $slug)';
  }

  @override
  int get hashCode {
    return name.hashCode ^ title.hashCode ^ slug.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CompanyIn) return false;
    return name == other.name && title == other.title && slug == other.slug;
  }

}
