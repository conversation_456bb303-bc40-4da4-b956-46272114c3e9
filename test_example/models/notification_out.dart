// Generated model class for NotificationOut
// This file is auto-generated. Do not edit manually.

import 'models.dart';

/// Schema for notification output
class NotificationOut {
  final int id;

  final CustomUserOut user;

  final String title;

  final String body;

  final bool isRead;

  final String createdAt;

  final String updatedAt;

  const NotificationOut({
    required this.id,
    required this.user,
    required this.title,
    required this.body,
    required this.isRead,
    required this.createdAt,
    required this.updatedAt,
  });

  factory NotificationOut.fromJson(Map<String, dynamic> json) {
    return NotificationOut(
      id: json['id'] as int? ?? 0,
      user: CustomUserOut.fromJson(json['user'] as Map<String, dynamic>? ?? {}),
      title: json['title']?.toString() ?? '',
      body: json['body']?.toString() ?? '',
      isRead: json['is_read'] as bool? ?? false,
      createdAt: json['created_at']?.toString() ?? '',
      updatedAt: json['updated_at']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user': user.toJson(),
      'title': title,
      'body': body,
      'is_read': isRead,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  @override
  String toString() {
    return 'NotificationOut(id: $id, user: $user, title: $title, body: $body, is_read: $isRead, created_at: $createdAt, updated_at: $updatedAt)';
  }

  @override
  int get hashCode {
    return id.hashCode ^ user.hashCode ^ title.hashCode ^ body.hashCode ^ isRead.hashCode ^ createdAt.hashCode ^ updatedAt.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! NotificationOut) return false;
    return id == other.id && user == other.user && title == other.title && body == other.body && isRead == other.isRead && createdAt == other.createdAt && updatedAt == other.updatedAt;
  }

}
