// Generated model class for StoreIn
// This file is auto-generated. Do not edit manually.

/// Schema for creating a new store
class StoreIn {
  final String name;

  final String? description;

  final String address;

  final int? cityId;

  final int? stateId;

  final int? countryId;

  final int ownerId;

  const StoreIn({
    required this.name,
    this.description,
    required this.address,
    this.cityId,
    this.stateId,
    this.countryId,
    required this.ownerId,
  });

  factory StoreIn.fromJson(Map<String, dynamic> json) {
    return StoreIn(
      name: json['name']?.toString() ?? '',
      description: json['description'] as String?,
      address: json['address']?.toString() ?? '',
      cityId: json['city_id'] as int?,
      stateId: json['state_id'] as int?,
      countryId: json['country_id'] as int?,
      ownerId: json['owner_id'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'address': address,
      'city_id': cityId,
      'state_id': stateId,
      'country_id': countryId,
      'owner_id': ownerId,
    };
  }

  @override
  String toString() {
    return 'StoreIn(name: $name, description: $description, address: $address, city_id: $cityId, state_id: $stateId, country_id: $countryId, owner_id: $ownerId)';
  }

  @override
  int get hashCode {
    return name.hashCode ^ description.hashCode ^ address.hashCode ^ cityId.hashCode ^ stateId.hashCode ^ countryId.hashCode ^ ownerId.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! StoreIn) return false;
    return name == other.name && description == other.description && address == other.address && cityId == other.cityId && stateId == other.stateId && countryId == other.countryId && ownerId == other.ownerId;
  }

}
