// Generated model class for CategoryOut
// This file is auto-generated. Do not edit manually.

/// Schema for category output
class CategoryOut {
  final int id;

  final String name;

  final String title;

  final String slug;

  final String createdAt;

  final String updatedAt;

  const CategoryOut({
    required this.id,
    required this.name,
    required this.title,
    required this.slug,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CategoryOut.fromJson(Map<String, dynamic> json) {
    return CategoryOut(
      id: json['id'] as int? ?? 0,
      name: json['name']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      slug: json['slug']?.toString() ?? '',
      createdAt: json['created_at']?.toString() ?? '',
      updatedAt: json['updated_at']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'title': title,
      'slug': slug,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  @override
  String toString() {
    return 'CategoryOut(id: $id, name: $name, title: $title, slug: $slug, created_at: $createdAt, updated_at: $updatedAt)';
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ title.hashCode ^ slug.hashCode ^ createdAt.hashCode ^ updatedAt.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CategoryOut) return false;
    return id == other.id && name == other.name && title == other.title && slug == other.slug && createdAt == other.createdAt && updatedAt == other.updatedAt;
  }

}
