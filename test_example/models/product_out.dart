// Generated model class for ProductOut
// This file is auto-generated. Do not edit manually.

import 'models.dart';

/// Schema for product output
class ProductOut {
  final int id;

  final String name;

  final String title;

  final String barcode;

  final String slug;

  final String description;

  final String? imageUrl;

  final CompanyOut? company;

  final CategoryOut? category;

  final String unit;

  final double unitCount;

  final int itemsCount;

  final String createdAt;

  final String updatedAt;

  const ProductOut({
    required this.id,
    required this.name,
    required this.title,
    required this.barcode,
    required this.slug,
    required this.description,
    this.imageUrl,
    this.company,
    this.category,
    required this.unit,
    required this.unitCount,
    required this.itemsCount,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ProductOut.fromJson(Map<String, dynamic> json) {
    return ProductOut(
      id: json['id'] as int? ?? 0,
      name: json['name']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      barcode: json['barcode']?.toString() ?? '',
      slug: json['slug']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      imageUrl: json['image_url'] as String?,
      company: json['company'] != null ? CompanyOut.fromJson(json['company'] as Map<String, dynamic>) : null,
      category: json['category'] != null ? CategoryOut.fromJson(json['category'] as Map<String, dynamic>) : null,
      unit: json['unit']?.toString() ?? '',
      unitCount: (json['unit_count'] as num?)?.toDouble() ?? 0.0,
      itemsCount: json['items_count'] as int? ?? 0,
      createdAt: json['created_at']?.toString() ?? '',
      updatedAt: json['updated_at']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'title': title,
      'barcode': barcode,
      'slug': slug,
      'description': description,
      'image_url': imageUrl,
      'company': company?.toJson(),
      'category': category?.toJson(),
      'unit': unit,
      'unit_count': unitCount,
      'items_count': itemsCount,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  @override
  String toString() {
    return 'ProductOut(id: $id, name: $name, title: $title, barcode: $barcode, slug: $slug, description: $description, image_url: $imageUrl, company: $company, category: $category, unit: $unit, unit_count: $unitCount, items_count: $itemsCount, created_at: $createdAt, updated_at: $updatedAt)';
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ title.hashCode ^ barcode.hashCode ^ slug.hashCode ^ description.hashCode ^ imageUrl.hashCode ^ company.hashCode ^ category.hashCode ^ unit.hashCode ^ unitCount.hashCode ^ itemsCount.hashCode ^ createdAt.hashCode ^ updatedAt.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ProductOut) return false;
    return id == other.id && name == other.name && title == other.title && barcode == other.barcode && slug == other.slug && description == other.description && imageUrl == other.imageUrl && company == other.company && category == other.category && unit == other.unit && unitCount == other.unitCount && itemsCount == other.itemsCount && createdAt == other.createdAt && updatedAt == other.updatedAt;
  }

}
