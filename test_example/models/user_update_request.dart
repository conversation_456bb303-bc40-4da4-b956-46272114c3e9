// Generated model class for UserUpdateRequest
// This file is auto-generated. Do not edit manually.

/// Request schema for updating user data
class UserUpdateRequest {
  final String? firstName;

  final String? lastName;

  final String? email;

  final String? phone;

  const UserUpdateRequest({
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
  });

  factory UserUpdateRequest.fromJson(Map<String, dynamic> json) {
    return UserUpdateRequest(
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'first_name': firstName,
      'last_name': lastName,
      'email': email,
      'phone': phone,
    };
  }

  @override
  String toString() {
    return 'UserUpdateRequest(first_name: $firstName, last_name: $lastName, email: $email, phone: $phone)';
  }

  @override
  int get hashCode {
    return firstName.hashCode ^ lastName.hashCode ^ email.hashCode ^ phone.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! UserUpdateRequest) return false;
    return firstName == other.firstName && lastName == other.lastName && email == other.email && phone == other.phone;
  }

}
