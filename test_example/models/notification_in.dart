// Generated model class for NotificationIn
// This file is auto-generated. Do not edit manually.

/// Schema for creating a new notification
class NotificationIn {
  final int userId;

  final String title;

  final String body;

  const NotificationIn({
    required this.userId,
    required this.title,
    required this.body,
  });

  factory NotificationIn.fromJson(Map<String, dynamic> json) {
    return NotificationIn(
      userId: json['user_id'] as int? ?? 0,
      title: json['title']?.toString() ?? '',
      body: json['body']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'title': title,
      'body': body,
    };
  }

  @override
  String toString() {
    return 'NotificationIn(user_id: $userId, title: $title, body: $body)';
  }

  @override
  int get hashCode {
    return userId.hashCode ^ title.hashCode ^ body.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! NotificationIn) return false;
    return userId == other.userId && title == other.title && body == other.body;
  }

}
