// Generated model class for LoginResponse
// This file is auto-generated. Do not edit manually.

class LoginResponse {
  final bool success;

  final String token;

  final int userId;

  final String phone;

  final bool isPhoneVerified;

  final int? wholesalerId;

  const LoginResponse({
    required this.success,
    required this.token,
    required this.userId,
    required this.phone,
    required this.isPhoneVerified,
    this.wholesalerId,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      success: json['success'] as bool? ?? false,
      token: json['token']?.toString() ?? '',
      userId: json['user_id'] as int? ?? 0,
      phone: json['phone']?.toString() ?? '',
      isPhoneVerified: json['is_phone_verified'] as bool? ?? false,
      wholesalerId: json['wholesaler_id'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'token': token,
      'user_id': userId,
      'phone': phone,
      'is_phone_verified': isPhoneVerified,
      'wholesaler_id': wholesalerId,
    };
  }

  @override
  String toString() {
    return 'LoginResponse(success: $success, token: $token, user_id: $userId, phone: $phone, is_phone_verified: $isPhoneVerified, wholesaler_id: $wholesalerId)';
  }

  @override
  int get hashCode {
    return success.hashCode ^ token.hashCode ^ userId.hashCode ^ phone.hashCode ^ isPhoneVerified.hashCode ^ wholesalerId.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! LoginResponse) return false;
    return success == other.success && token == other.token && userId == other.userId && phone == other.phone && isPhoneVerified == other.isPhoneVerified && wholesalerId == other.wholesalerId;
  }

}
