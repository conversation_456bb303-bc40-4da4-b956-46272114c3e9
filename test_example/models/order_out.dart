// Generated model class for OrderOut
// This file is auto-generated. Do not edit manually.

import 'models.dart';

/// Schema for order output
class OrderOut {
  final int id;

  final WholesalerOut wholesaler;

  final StoreOut store;

  final double totalPrice;

  final double fees;

  final String? deliverAt;

  final double productsTotalPrice;

  final int productsTotalQuantity;

  final double? finalCompletedPrice;

  final String? completedAt;

  final String status;

  final String? statusReason;

  final String statusUpdatedAt;

  final String createdAt;

  final String updatedAt;

  const OrderOut({
    required this.id,
    required this.wholesaler,
    required this.store,
    required this.totalPrice,
    required this.fees,
    this.deliverAt,
    required this.productsTotalPrice,
    required this.productsTotalQuantity,
    this.finalCompletedPrice,
    this.completedAt,
    required this.status,
    this.statusReason,
    required this.statusUpdatedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory OrderOut.fromJson(Map<String, dynamic> json) {
    return OrderOut(
      id: json['id'] as int? ?? 0,
      wholesaler: WholesalerOut.fromJson(json['wholesaler'] as Map<String, dynamic>? ?? {}),
      store: StoreOut.fromJson(json['store'] as Map<String, dynamic>? ?? {}),
      totalPrice: (json['total_price'] as num?)?.toDouble() ?? 0.0,
      fees: (json['fees'] as num?)?.toDouble() ?? 0.0,
      deliverAt: json['deliver_at'] as String?,
      productsTotalPrice: (json['products_total_price'] as num?)?.toDouble() ?? 0.0,
      productsTotalQuantity: json['products_total_quantity'] as int? ?? 0,
      finalCompletedPrice: (json['final_completed_price'] as num?)?.toDouble(),
      completedAt: json['completed_at'] as String?,
      status: json['status']?.toString() ?? '',
      statusReason: json['status_reason'] as String?,
      statusUpdatedAt: json['status_updated_at']?.toString() ?? '',
      createdAt: json['created_at']?.toString() ?? '',
      updatedAt: json['updated_at']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'wholesaler': wholesaler.toJson(),
      'store': store.toJson(),
      'total_price': totalPrice,
      'fees': fees,
      'deliver_at': deliverAt,
      'products_total_price': productsTotalPrice,
      'products_total_quantity': productsTotalQuantity,
      'final_completed_price': finalCompletedPrice,
      'completed_at': completedAt,
      'status': status,
      'status_reason': statusReason,
      'status_updated_at': statusUpdatedAt,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  @override
  String toString() {
    return 'OrderOut(id: $id, wholesaler: $wholesaler, store: $store, total_price: $totalPrice, fees: $fees, deliver_at: $deliverAt, products_total_price: $productsTotalPrice, products_total_quantity: $productsTotalQuantity, final_completed_price: $finalCompletedPrice, completed_at: $completedAt, status: $status, status_reason: $statusReason, status_updated_at: $statusUpdatedAt, created_at: $createdAt, updated_at: $updatedAt)';
  }

  @override
  int get hashCode {
    return id.hashCode ^ wholesaler.hashCode ^ store.hashCode ^ totalPrice.hashCode ^ fees.hashCode ^ deliverAt.hashCode ^ productsTotalPrice.hashCode ^ productsTotalQuantity.hashCode ^ finalCompletedPrice.hashCode ^ completedAt.hashCode ^ status.hashCode ^ statusReason.hashCode ^ statusUpdatedAt.hashCode ^ createdAt.hashCode ^ updatedAt.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! OrderOut) return false;
    return id == other.id && wholesaler == other.wholesaler && store == other.store && totalPrice == other.totalPrice && fees == other.fees && deliverAt == other.deliverAt && productsTotalPrice == other.productsTotalPrice && productsTotalQuantity == other.productsTotalQuantity && finalCompletedPrice == other.finalCompletedPrice && completedAt == other.completedAt && status == other.status && statusReason == other.statusReason && statusUpdatedAt == other.statusUpdatedAt && createdAt == other.createdAt && updatedAt == other.updatedAt;
  }

}
