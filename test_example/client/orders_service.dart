// Generated service class for orders operations
// This file is auto-generated. Do not edit manually.

import 'package:dio/dio.dart';
import '../models/models.dart';
import 'package:openapi_dart_gen/openapi_dart_gen.dart';

/// Service class for orders operations
class OrdersService {
  final Dio _dio;

  const OrdersService(this._dio);

  /// List Orders
  ///
  /// List all orders with pagination and filtering.
  /// Requires authentication.
  ///
  Future<Response<PaginatedOrderResponse>> storesApiOrderListOrders({int? page, int? pageSize, String? status, int? storeId, int? wholesalerId}) async {
    const path = '/api/orders/';
    final queryParameters = <String, dynamic>{};
    if (page != null) {
      queryParameters['page'] = page;
    }
    if (pageSize != null) {
      queryParameters['page_size'] = pageSize;
    }
    if (status != null) {
      queryParameters['status'] = status;
    }
    if (storeId != null) {
      queryParameters['store_id'] = storeId;
    }
    if (wholesalerId != null) {
      queryParameters['wholesaler_id'] = wholesalerId;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseData = PaginatedOrderResponse.fromJson(response.data as Map<String, dynamic>);
      return Response<PaginatedOrderResponse>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Create Order
  ///
  /// Create a new order.
  /// Requires authentication.
  ///
  Future<Response<OrderOut>> storesApiOrderCreateOrder(OrderIn requestData) async {
    const path = '/api/orders/';
    try {
      final response = await _dio.post(path, data: requestData.toJson());
      final responseData = OrderOut.fromJson(response.data as Map<String, dynamic>);
      return Response<OrderOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Get Order
  ///
  /// Get a specific order by ID.
  /// Requires authentication.
  ///
  Future<Response<OrderOut>> storesApiOrderGetOrder(int orderId) async {
    String path = '/api/orders/{order_id}';
    path = path.replaceAll('{order_id}', orderId.toString());
    try {
      final response = await _dio.get(path);
      final responseData = OrderOut.fromJson(response.data as Map<String, dynamic>);
      return Response<OrderOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Update Order
  ///
  /// Update an order.
  /// Requires authentication.
  ///
  Future<Response<OrderOut>> storesApiOrderUpdateOrder(int orderId, OrderUpdate requestData) async {
    String path = '/api/orders/{order_id}';
    path = path.replaceAll('{order_id}', orderId.toString());
    try {
      final response = await _dio.put(path, data: requestData.toJson());
      final responseData = OrderOut.fromJson(response.data as Map<String, dynamic>);
      return Response<OrderOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Delete Order
  ///
  /// Soft delete an order.
  /// Requires authentication.
  ///
  Future<Response<dynamic>> storesApiOrderDeleteOrder(int orderId) async {
    String path = '/api/orders/{order_id}';
    path = path.replaceAll('{order_id}', orderId.toString());
    try {
      final response = await _dio.delete(path);
      return response;
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

}
