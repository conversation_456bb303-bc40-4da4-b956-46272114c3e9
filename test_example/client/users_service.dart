// Generated service class for users operations
// This file is auto-generated. Do not edit manually.

import 'package:dio/dio.dart';
import '../models/models.dart';
import 'package:openapi_dart_gen/openapi_dart_gen.dart';

/// Service class for users operations
class UsersService {
  final Dio _dio;

  const UsersService(this._dio);

  /// List Users
  ///
  /// List all users with pagination and filtering.
  /// Requires authentication.
  ///
  Future<Response<PaginatedCustomUserResponse>> accountsApiCustomUserListUsers({int? page, int? pageSize, String? search, bool? isActive, bool? phoneVerified}) async {
    const path = '/api/users/';
    final queryParameters = <String, dynamic>{};
    if (page != null) {
      queryParameters['page'] = page;
    }
    if (pageSize != null) {
      queryParameters['page_size'] = pageSize;
    }
    if (search != null) {
      queryParameters['search'] = search;
    }
    if (isActive != null) {
      queryParameters['is_active'] = isActive;
    }
    if (phoneVerified != null) {
      queryParameters['phone_verified'] = phoneVerified;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseData = PaginatedCustomUserResponse.fromJson(response.data as Map<String, dynamic>);
      return Response<PaginatedCustomUserResponse>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Create User
  ///
  /// Create a new user.
  /// Requires authentication.
  ///
  Future<Response<CustomUserOut>> accountsApiCustomUserCreateUser(CustomUserIn requestData) async {
    const path = '/api/users/';
    try {
      final response = await _dio.post(path, data: requestData.toJson());
      final responseData = CustomUserOut.fromJson(response.data as Map<String, dynamic>);
      return Response<CustomUserOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Get User
  ///
  /// Get a specific user by ID.
  /// Requires authentication.
  ///
  Future<Response<CustomUserOut>> accountsApiCustomUserGetUser(int userId) async {
    String path = '/api/users/{user_id}';
    path = path.replaceAll('{user_id}', userId.toString());
    try {
      final response = await _dio.get(path);
      final responseData = CustomUserOut.fromJson(response.data as Map<String, dynamic>);
      return Response<CustomUserOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Update User
  ///
  /// Update a user.
  /// Requires authentication.
  ///
  Future<Response<CustomUserOut>> accountsApiCustomUserUpdateUser(int userId, CustomUserUpdate requestData) async {
    String path = '/api/users/{user_id}';
    path = path.replaceAll('{user_id}', userId.toString());
    try {
      final response = await _dio.put(path, data: requestData.toJson());
      final responseData = CustomUserOut.fromJson(response.data as Map<String, dynamic>);
      return Response<CustomUserOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Delete User
  ///
  /// Soft delete a user.
  /// Requires authentication.
  ///
  Future<Response<dynamic>> accountsApiCustomUserDeleteUser(int userId) async {
    String path = '/api/users/{user_id}';
    path = path.replaceAll('{user_id}', userId.toString());
    try {
      final response = await _dio.delete(path);
      return response;
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

}
