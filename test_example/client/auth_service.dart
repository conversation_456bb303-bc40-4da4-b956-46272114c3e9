// Generated service class for auth operations
// This file is auto-generated. Do not edit manually.

import 'package:dio/dio.dart';
import '../models/models.dart';
import 'package:openapi_dart_gen/openapi_dart_gen.dart';

/// Service class for auth operations
class AuthService {
  final Dio _dio;

  const AuthService(this._dio);

  /// Signin
  ///
  /// User authentication endpoint
  /// Returns JWT token and user details including wholesaler_id if applicable
  ///
  Future<Response<LoginResponse>> coreApiSignin(LoginRequest requestData) async {
    const path = '/api/signin';
    try {
      final response = await _dio.post(path, data: requestData.toJson());
      final responseData = LoginResponse.fromJson(response.data as Map<String, dynamic>);
      return Response<LoginResponse>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Signup
  ///
  /// User registration endpoint
  /// Creates user account with phone verification set to False
  ///
  Future<Response<RegisterResponse>> coreApiSignup(RegisterRequest requestData) async {
    const path = '/api/signup';
    try {
      final response = await _dio.post(path, data: requestData.toJson());
      final responseData = RegisterResponse.fromJson(response.data as Map<String, dynamic>);
      return Response<RegisterResponse>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Get Current User
  ///
  /// Get the current authenticated user's data
  /// Requires authentication via JWT token
  ///
  Future<Response<UserResponse>> coreApiGetCurrentUser() async {
    const path = '/api/me';
    try {
      final response = await _dio.get(path);
      final responseData = UserResponse.fromJson(response.data as Map<String, dynamic>);
      return Response<UserResponse>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Update Current User
  ///
  /// Update the current authenticated user's data
  /// Requires authentication via JWT token
  ///
  Future<Response<UserResponse>> coreApiUpdateCurrentUser(UserUpdateRequest requestData) async {
    const path = '/api/me';
    try {
      final response = await _dio.put(path, data: requestData.toJson());
      final responseData = UserResponse.fromJson(response.data as Map<String, dynamic>);
      return Response<UserResponse>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

}
