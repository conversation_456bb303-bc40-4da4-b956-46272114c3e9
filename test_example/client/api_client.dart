// Generated API client for NinjaAPI
// This file is auto-generated. Do not edit manually.

import 'package:dio/dio.dart';

import 'core_service.dart';
import 'auth_service.dart';
import 'home_service.dart';
import 'users_service.dart';
import 'notifications_service.dart';
import 'products_service.dart';
import 'categories_service.dart';
import 'companies_service.dart';
import 'regions_service.dart';
import 'stores_service.dart';
import 'orders_service.dart';
import 'wholesalers_service.dart';

/// API client for NinjaAPI
class ApiClient {
  final Dio _dio;

  late final CoreService coreTag;
  late final AuthService authTag;
  late final HomeService homeTag;
  late final UsersService usersTag;
  late final NotificationsService notificationsTag;
  late final ProductsService productsTag;
  late final CategoriesService categoriesTag;
  late final CompaniesService companiesTag;
  late final RegionsService regionsTag;
  late final StoresService storesTag;
  late final OrdersService ordersTag;
  late final WholesalersService wholesalersTag;

  ApiClient({
    String? baseUrl,
    Dio? dio,
    Map<String, String>? headers,
    Duration? connectTimeout,
    Duration? receiveTimeout,
  }) : _dio = dio ?? Dio() {
    // Configure base options
    _dio.options.baseUrl = baseUrl ?? '';
    _dio.options.connectTimeout = connectTimeout ?? const Duration(seconds: 30);
    _dio.options.receiveTimeout = receiveTimeout ?? const Duration(seconds: 30);

    // Set default headers
    _dio.options.headers['Content-Type'] = 'application/json';
    _dio.options.headers['Accept'] = 'application/json';

    // Add custom headers
    if (headers != null) {
      _dio.options.headers.addAll(headers);
    }

    coreTag = CoreService(_dio);
    authTag = AuthService(_dio);
    homeTag = HomeService(_dio);
    usersTag = UsersService(_dio);
    notificationsTag = NotificationsService(_dio);
    productsTag = ProductsService(_dio);
    categoriesTag = CategoriesService(_dio);
    companiesTag = CompaniesService(_dio);
    regionsTag = RegionsService(_dio);
    storesTag = StoresService(_dio);
    ordersTag = OrdersService(_dio);
    wholesalersTag = WholesalersService(_dio);
  }

  /// Access to the underlying Dio instance
  Dio get dio => _dio;

  /// Add an interceptor to the Dio instance
  void addInterceptor(Interceptor interceptor) {
    _dio.interceptors.add(interceptor);
  }

  /// Set authorization header
  void setAuthorizationHeader(String token, {String scheme = "Bearer"}) {
    _dio.options.headers["Authorization"] = "$scheme $token";
  }

  /// Remove authorization header
  void removeAuthorizationHeader() {
    _dio.options.headers.remove("Authorization");
  }
}
