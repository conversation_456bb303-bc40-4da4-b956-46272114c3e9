// Generated service class for home operations
// This file is auto-generated. Do not edit manually.

import 'package:dio/dio.dart';
import '../models/models.dart';
import 'package:openapi_dart_gen/openapi_dart_gen.dart';

/// Service class for home operations
class HomeService {
  final Dio _dio;

  const HomeService(this._dio);

  /// Get Home Products
  ///
  /// Get products for home page with pagination and filtering support.
  /// 
  /// Query Parameters:
  /// - page: Page number (default: 1)
  /// - page_size: Number of items per page (default: 20, max: 100)
  /// - region_id: Filter by region ID (includes parent regions)
  /// - wholesaler_id: Filter by specific wholesaler ID
  /// - search: Search term for product name/title/description
  /// - category_id: Filter by category ID
  /// - company_id: Filter by company ID
  ///
  Future<Response<PaginatedProductResponse>> coreApiGetHomeProducts({int? page, int? pageSize, int? regionId, int? wholesalerId, String? search, int? categoryId, int? companyId}) async {
    const path = '/api/home/<USER>';
    final queryParameters = <String, dynamic>{};
    if (page != null) {
      queryParameters['page'] = page;
    }
    if (pageSize != null) {
      queryParameters['page_size'] = pageSize;
    }
    if (regionId != null) {
      queryParameters['region_id'] = regionId;
    }
    if (wholesalerId != null) {
      queryParameters['wholesaler_id'] = wholesalerId;
    }
    if (search != null) {
      queryParameters['search'] = search;
    }
    if (categoryId != null) {
      queryParameters['category_id'] = categoryId;
    }
    if (companyId != null) {
      queryParameters['company_id'] = companyId;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseData = PaginatedProductResponse.fromJson(response.data as Map<String, dynamic>);
      return Response<PaginatedProductResponse>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

}
