// Generated service class for products operations
// This file is auto-generated. Do not edit manually.

import 'package:dio/dio.dart';
import '../models/models.dart';
import 'package:openapi_dart_gen/openapi_dart_gen.dart';

/// Service class for products operations
class ProductsService {
  final Dio _dio;

  const ProductsService(this._dio);

  /// List Products
  ///
  /// List all products with pagination and filtering.
  /// Requires authentication.
  ///
  Future<Response<PaginatedProductResponse>> productsApiProductListProducts({int? page, int? pageSize, String? search, int? companyId, int? categoryId, String? unit}) async {
    const path = '/api/products/';
    final queryParameters = <String, dynamic>{};
    if (page != null) {
      queryParameters['page'] = page;
    }
    if (pageSize != null) {
      queryParameters['page_size'] = pageSize;
    }
    if (search != null) {
      queryParameters['search'] = search;
    }
    if (companyId != null) {
      queryParameters['company_id'] = companyId;
    }
    if (categoryId != null) {
      queryParameters['category_id'] = categoryId;
    }
    if (unit != null) {
      queryParameters['unit'] = unit;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseData = PaginatedProductResponse.fromJson(response.data as Map<String, dynamic>);
      return Response<PaginatedProductResponse>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Create Product
  ///
  /// Create a new product.
  /// Requires authentication.
  ///
  Future<Response<ProductOut>> productsApiProductCreateProduct(ProductIn requestData) async {
    const path = '/api/products/';
    try {
      final response = await _dio.post(path, data: requestData.toJson());
      final responseData = ProductOut.fromJson(response.data as Map<String, dynamic>);
      return Response<ProductOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Get Product
  ///
  /// Get a specific product by ID.
  /// Requires authentication.
  ///
  Future<Response<ProductOut>> productsApiProductGetProduct(int productId) async {
    String path = '/api/products/{product_id}';
    path = path.replaceAll('{product_id}', productId.toString());
    try {
      final response = await _dio.get(path);
      final responseData = ProductOut.fromJson(response.data as Map<String, dynamic>);
      return Response<ProductOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Update Product
  ///
  /// Update a product.
  /// Requires authentication.
  ///
  Future<Response<ProductOut>> productsApiProductUpdateProduct(int productId, ProductUpdate requestData) async {
    String path = '/api/products/{product_id}';
    path = path.replaceAll('{product_id}', productId.toString());
    try {
      final response = await _dio.put(path, data: requestData.toJson());
      final responseData = ProductOut.fromJson(response.data as Map<String, dynamic>);
      return Response<ProductOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Delete Product
  ///
  /// Soft delete a product.
  /// Requires authentication.
  ///
  Future<Response<dynamic>> productsApiProductDeleteProduct(int productId) async {
    String path = '/api/products/{product_id}';
    path = path.replaceAll('{product_id}', productId.toString());
    try {
      final response = await _dio.delete(path);
      return response;
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

}
