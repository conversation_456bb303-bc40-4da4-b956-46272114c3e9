// Generated service class for companies operations
// This file is auto-generated. Do not edit manually.

import 'package:dio/dio.dart';
import '../models/models.dart';
import 'package:openapi_dart_gen/openapi_dart_gen.dart';

/// Service class for companies operations
class CompaniesService {
  final Dio _dio;

  const CompaniesService(this._dio);

  /// List Companies
  ///
  /// List all companies with pagination and filtering.
  /// Requires authentication.
  ///
  Future<Response<PaginatedCompanyResponse>> productsApiCompanyListCompanies({int? page, int? pageSize, String? search}) async {
    const path = '/api/companies/';
    final queryParameters = <String, dynamic>{};
    if (page != null) {
      queryParameters['page'] = page;
    }
    if (pageSize != null) {
      queryParameters['page_size'] = pageSize;
    }
    if (search != null) {
      queryParameters['search'] = search;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseData = PaginatedCompanyResponse.fromJson(response.data as Map<String, dynamic>);
      return Response<PaginatedCompanyResponse>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Create Company
  ///
  /// Create a new company.
  /// Requires authentication.
  ///
  Future<Response<CompanyOut>> productsApiCompanyCreateCompany(CompanyIn requestData) async {
    const path = '/api/companies/';
    try {
      final response = await _dio.post(path, data: requestData.toJson());
      final responseData = CompanyOut.fromJson(response.data as Map<String, dynamic>);
      return Response<CompanyOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Get Company
  ///
  /// Get a specific company by ID.
  /// Requires authentication.
  ///
  Future<Response<CompanyOut>> productsApiCompanyGetCompany(int companyId) async {
    String path = '/api/companies/{company_id}';
    path = path.replaceAll('{company_id}', companyId.toString());
    try {
      final response = await _dio.get(path);
      final responseData = CompanyOut.fromJson(response.data as Map<String, dynamic>);
      return Response<CompanyOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Update Company
  ///
  /// Update a company.
  /// Requires authentication.
  ///
  Future<Response<CompanyOut>> productsApiCompanyUpdateCompany(int companyId, CompanyUpdate requestData) async {
    String path = '/api/companies/{company_id}';
    path = path.replaceAll('{company_id}', companyId.toString());
    try {
      final response = await _dio.put(path, data: requestData.toJson());
      final responseData = CompanyOut.fromJson(response.data as Map<String, dynamic>);
      return Response<CompanyOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Delete Company
  ///
  /// Soft delete a company.
  /// Requires authentication.
  ///
  Future<Response<dynamic>> productsApiCompanyDeleteCompany(int companyId) async {
    String path = '/api/companies/{company_id}';
    path = path.replaceAll('{company_id}', companyId.toString());
    try {
      final response = await _dio.delete(path);
      return response;
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Get Company Products
  ///
  /// Get all products from a company.
  /// Requires authentication.
  ///
  Future<Response<dynamic>> productsApiCompanyGetCompanyProducts(int companyId) async {
    String path = '/api/companies/{company_id}/products';
    path = path.replaceAll('{company_id}', companyId.toString());
    try {
      final response = await _dio.get(path);
      return response;
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

}
